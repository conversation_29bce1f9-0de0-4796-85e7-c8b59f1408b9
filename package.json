{"name": "modularcxtesting-3dviewer", "version": "1.0.0", "license": "UNLICENSED", "scripts": {"shopify": "shopify", "build": "shopify app build", "dev": "shopify app dev", "info": "shopify app info", "generate": "shopify app generate", "predeploy": "node deploy-checklist.js", "deploy": "shopify app deploy", "start": "node server.js", "check": "node deploy-checklist.js", "deploy:full": "node deploy-app.js", "verify:webhooks": "node verify-webhooks.js"}, "dependencies": {"@shopify/shopify-api": "^8.0.2", "express": "^4.18.2", "dotenv": "^16.3.1", "crypto": "^1.0.1", "body-parser": "^1.20.2", "node-fetch": "^2.7.0", "@shopify/shopify-app-express": "^2.1.3", "jsonwebtoken": "^9.0.2"}, "trustedDependencies": ["@shopify/plugin-cloudflare"], "author": "<PERSON><PERSON>", "private": true, "workspaces": ["extensions/*"], "devDependencies": {"nodemon": "^3.0.1"}}