<div id="glb-container-{{ section.id }}"></div>

<script type="module">
    document.addEventListener("DOMContentLoaded", async function() {
        const container = document.getElementById("glb-container-{{ section.id }}");
        let modelViewer = null;

        // Function to load 3D model
        async function load3DModel(productId, variantId = null) {
            try {
                if (!productId) {
                    throw new Error("Product ID not found");
                }

                // Build API URL with product ID and variant ID (if available)
                let apiUrl = `https://api.modularcx.link/morpho/shopify/test?productId=${productId}`;
                if (variantId) {
                    apiUrl += `&shopifyVariantId=${variantId}`;
                }


                // Fetch the 3D model URL from the API
                const response = await fetch(apiUrl);
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.message || "Failed to fetch model URL");
                }
                
                const modelUrl = data.url;

                if (!modelUrl) {
                    throw new Error("3D Model URL is missing from API response");
                }

                // If model viewer already exists, just update the source
                if (modelViewer) {
                    modelViewer.src = modelUrl;
                } else {
                    // Create and append the model viewer
                    modelViewer = document.createElement("model-viewer");
                    modelViewer.src = modelUrl;
                    modelViewer.alt = "3D Model";
                    modelViewer.autoRotate = true;
                    modelViewer.cameraControls = true;
                    modelViewer.style.width = "100%";
                    modelViewer.style.height = "500px";
                    
                    container.appendChild(modelViewer);
                }
            } catch (error) {
                console.error("Error loading GLB model:", error);
                container.innerHTML = `<p>3D model not available for this product</p>`;
            }
        }

        // Get initial product ID and selected variant ID
        const productId = "{{ product.id }}";
        let currentVariantId = "{{ product.selected_or_first_available_variant.id }}";
        
        // Load initial 3D model
        await load3DModel(productId, currentVariantId);

        // Listen for variant changes (works with most modern Shopify themes)
        document.addEventListener('variant:change', function(event) {
            if (event.detail && event.detail.variant && event.detail.variant.id) {
                currentVariantId = event.detail.variant.id;
                load3DModel(productId, currentVariantId);
            }
        });

        // Alternative method for themes that don't support variant:change event
        const variantSelectors = document.querySelectorAll('[name^="id"], [data-product-select]');
        variantSelectors.forEach(selector => {
            selector.addEventListener('change', function(event) {
                const newVariantId = event.target.value;
                if (newVariantId && newVariantId !== currentVariantId) {
                    currentVariantId = newVariantId;
                    load3DModel(productId, currentVariantId);
                }
            });
        });
    });
</script>

<!-- Load model-viewer -->
<script type="module" src="https://unpkg.com/@google/model-viewer"></script>

{% schema %}
{
  "name": "Morpho 3D Viewer",
  "target": "section",
  "settings": [
    {
      "type": "text",
      "id": "viewerUrl",
      "label": "3D Viewer URL"
    }
  ]
}
{% endschema %}