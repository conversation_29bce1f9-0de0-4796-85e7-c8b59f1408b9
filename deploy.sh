#!/bin/bash

# Deploy script for 3D Viewer Shopify App

echo "Starting deployment process..."

# Install dependencies
echo "Installing dependencies..."
npm install

# Build the app
echo "Building the app..."
npm run build

# Deploy to Shopify
echo "Deploying to Shopify..."
npm run deploy

echo "Deployment complete!"
echo "Please verify the following in the Shopify Partners dashboard:"
echo "1. App is properly installed"
echo "2. Webhooks are properly configured"
echo "3. Authentication is working correctly"
echo "4. App UI is accessible"
