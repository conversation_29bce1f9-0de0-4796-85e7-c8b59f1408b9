# Deployment Guide for 3D Viewer Shopify App

This guide will help you deploy your Shopify app and ensure it passes all the automated checks required by Shopify.

## Prerequisites

1. A Shopify Partner account
2. Your app registered in the Shopify Partner Dashboard
3. Node.js installed on your machine
4. Access to your app's API key and API secret key

## Pre-Deployment Checklist

Before deploying, ensure your app meets these requirements:

- [x] Mandatory compliance webhooks are configured
- [x] Webhook verification with HMAC signatures is implemented
- [x] Authentication flow is properly set up
- [x] App redirects to UI after authentication
- [x] App can be cleanly uninstalled and reinstalled

## Deployment Steps

### 1. Set Up Environment Variables

Make sure your `.env` file contains the following:

```
SHOPIFY_API_KEY=your_api_key_from_partner_dashboard
SHOPIFY_API_SECRET=your_api_secret_from_partner_dashboard
HOST=your-app-url.shopify.dev
PORT=3000
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Build the App

```bash
npm run build
```

### 4. Deploy to Shopify

```bash
npm run deploy
```

This will deploy your app to Shopify's infrastructure, which will handle TLS certificates and hosting.

## Post-Deployment Verification

After deployment, verify the following:

1. **Authentication Flow**:
   - Install the app on a test store
   - Verify that authentication happens immediately
   - Verify that it redirects to the app UI after authentication

2. **Webhooks**:
   - Verify that all mandatory webhooks are registered
   - Test the app uninstallation and reinstallation process

3. **TLS Certificate**:
   - Verify that your app is using a valid TLS certificate (Shopify handles this)

4. **App Functionality**:
   - Test the 3D viewer functionality on product pages
   - Verify that variant selection works correctly

## Troubleshooting

### Common Issues

1. **Webhook Verification Failures**:
   - Check that your SHOPIFY_API_SECRET is correctly set
   - Verify that the HMAC verification code is working correctly

2. **Authentication Issues**:
   - Verify that your redirect URLs are correctly configured in the Shopify Partner Dashboard
   - Check that your app's scopes are correctly set

3. **TLS Certificate Issues**:
   - If using Shopify's hosting, they handle TLS certificates
   - If self-hosting, ensure you have a valid certificate from a trusted CA

### Getting Help

If you encounter issues during deployment, you can:

1. Check the Shopify Developer Documentation: https://shopify.dev/docs/apps
2. Visit the Shopify Community Forums: https://community.shopify.com/
3. Contact Shopify Partner Support

## Maintaining Your App

After successful deployment:

1. Monitor your app's performance and error logs
2. Keep dependencies updated
3. Respond to webhook events appropriately
4. Follow Shopify's guidelines for app maintenance
