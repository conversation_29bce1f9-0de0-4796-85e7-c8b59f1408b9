# 3D Viewer Shopify App

This Shopify app allows merchants to add interactive 3D models to their product pages. The app includes a theme extension that adds a 3D viewer component to product pages.

## Features

- Interactive 3D model viewer for product pages
- Support for variant-specific 3D models
- Automatic model loading when variants are selected
- Responsive design that works on all devices

## Deployment Instructions

Follow these steps to deploy the app to Shopify:

1. **Install dependencies**:

   ```
   npm install
   ```

2. **Set up environment variables**:
   Make sure your `.env` file contains the following:

   ```
   SHOPIFY_API_KEY=your_api_key
   SHOPIFY_API_SECRET=your_api_secret
   HOST=your-app-url.shopify.dev
   PORT=3000
   ```

3. **Build the app**:

   ```
   npm run build
   ```

4. **Deploy to Shopify**:

   ```
   npm run deploy
   ```

5. **Verify webhook configuration**:
   Ensure all required webhooks are properly configured in the Shopify Partners dashboard.

## Getting started

### Requirements

1. You must [download and install Node.js](https://nodejs.org/en/download/) if you don't already have it.
1. You must [create a Shopify partner account](https://partners.shopify.com/signup) if you don’t have one.
1. You must create a store for testing if you don't have one, either a [development store](https://help.shopify.com/en/partners/dashboard/development-stores#create-a-development-store) or a [Shopify Plus sandbox store](https://help.shopify.com/en/partners/dashboard/managing-stores/plus-sandbox-store).

### Installing the template

This template can be installed using your preferred package manager:

Using yarn:

```shell
yarn create @shopify/app
```

Using npm:

```shell
npm init @shopify/app@latest
```

Using pnpm:

```shell
pnpm create @shopify/app@latest
```

This will clone the template and install the required dependencies.

#### Local Development

[The Shopify CLI](https://shopify.dev/docs/apps/tools/cli) connects to an app in your Partners dashboard. It provides environment variables and runs commands in parallel.

You can develop locally using your preferred package manager. Run one of the following commands from the root of your app.

Using yarn:

```shell
yarn dev
```

Using npm:

```shell
npm run dev
```

Using pnpm:

```shell
pnpm run dev
```

Open the URL generated in your console. Once you grant permission to the app, you can start development (such as generating extensions).

## Developer resources

- [Introduction to Shopify apps](https://shopify.dev/docs/apps/getting-started)
- [App extensions](https://shopify.dev/docs/apps/build/app-extensions)
- [Extension only apps](https://shopify.dev/docs/apps/build/app-extensions/build-extension-only-app)
- [Shopify CLI](https://shopify.dev/docs/apps/tools/cli)
