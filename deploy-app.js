#!/usr/bin/env node

/**
 * Shopify App Deployment Script
 *
 * This script performs a complete deployment of your Shopify app,
 * ensuring all required checks are passed.
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");

// Load environment variables
dotenv.config();

// ANSI color codes for console output
const colors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  dim: "\x1b[2m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  cyan: "\x1b[36m",
};

// Print header

// Check if required environment variables are set
function checkEnvironment() {
  const requiredVars = ["SHOPIFY_API_KEY", "SHOPIFY_API_SECRET", "HOST"];
  const missingVars = [];

  requiredVars.forEach((varName) => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    console.error(
      `${
        colors.red
      }Error: Missing required environment variables: ${missingVars.join(
        ", "
      )}${colors.reset}`
    );
    process.exit(1);
  }
}

// Verify app configuration
function verifyAppConfig() {
  try {
    // Check if shopify.app.toml exists
    const appConfigPath = path.join(process.cwd(), "shopify.app.toml");
    if (!fs.existsSync(appConfigPath)) {
      console.error(
        `${colors.red}Error: shopify.app.toml not found${colors.reset}`
      );
      process.exit(1);
    }

    const appConfig = fs.readFileSync(appConfigPath, "utf8");

    // Check for mandatory webhooks
    const requiredWebhooks = [
      "app/uninstalled",
      "shop/update",
      "shop/redact",
      "customers/data_request",
      "customers/redact",
    ];

    let missingWebhooks = [];
    requiredWebhooks.forEach((webhook) => {
      if (!appConfig.includes(webhook)) {
        missingWebhooks.push(webhook);
      }
    });

    if (missingWebhooks.length > 0) {
      console.error(
        `${colors.red}Error: Missing required webhooks: ${missingWebhooks.join(
          ", "
        )}${colors.reset}`
      );
      process.exit(1);
    }
  } catch (error) {
    console.error(
      `${colors.red}Error verifying app configuration: ${error.message}${colors.reset}`
    );
    process.exit(1);
  }
}

// Verify server implementation
function verifyServerImplementation() {
  try {
    // Check if server.js exists
    const serverPath = path.join(process.cwd(), "server.js");
    if (!fs.existsSync(serverPath)) {
      console.error(`${colors.red}Error: server.js not found${colors.reset}`);
      process.exit(1);
    }

    const serverFile = fs.readFileSync(serverPath, "utf8");

    // Check for HMAC verification
    if (
      !serverFile.includes("verifyShopifyWebhook") ||
      !serverFile.includes("hmac")
    ) {
      console.error(
        `${colors.red}Error: Missing webhook verification with HMAC signatures${colors.reset}`
      );
      process.exit(1);
    }

    // Check for webhook routes
    const requiredWebhookRoutes = [
      "/webhooks/app/uninstalled",
      "/webhooks/shop/update",
      "/webhooks/shop/redact",
      "/webhooks/customers/data_request",
      "/webhooks/customers/redact",
    ];

    let missingRoutes = [];
    requiredWebhookRoutes.forEach((route) => {
      if (!serverFile.includes(route)) {
        missingRoutes.push(route);
      }
    });

    if (missingRoutes.length > 0) {
      console.error(
        `${
          colors.red
        }Error: Missing required webhook routes: ${missingRoutes.join(", ")}${
          colors.reset
        }`
      );
      process.exit(1);
    }
  } catch (error) {
    console.error(
      `${colors.red}Error verifying server implementation: ${error.message}${colors.reset}`
    );
    process.exit(1);
  }
}

// Build the app
function buildApp() {
  try {
    execSync("npm run build", { stdio: "inherit" });
  } catch (error) {
    console.error(`${colors.red}Error building the app${colors.reset}`);
    process.exit(1);
  }
}

// Deploy the app
function deployApp() {
  try {
    execSync("shopify app deploy", { stdio: "inherit" });
  } catch (error) {
    console.error(`${colors.red}Error deploying the app${colors.reset}`);
    process.exit(1);
  }
}

// Main function
function main() {
  try {
    // Run all checks
    checkEnvironment();
    verifyAppConfig();
    verifyServerImplementation();

    // Build and deploy
    buildApp();
    deployApp();
  } catch (error) {
    console.error(
      `\n${colors.red}Deployment failed: ${error.message}${colors.reset}`
    );
    process.exit(1);
  }
}

// Run the main function
main();
