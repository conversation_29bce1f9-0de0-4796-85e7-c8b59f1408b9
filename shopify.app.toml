# Shopify App Configuration

client_id = "d36f16d2a6e8767e33a54d83ba172a41"
name = "ModularCX-3d-viewer"
handle = "modularcx-3d"
application_url = "https://morpho.modularcx.io"
embedded = false

[build]
dev_store_url = "modularcxtest.myshopify.com"
include_config_on_deploy = true

[webhooks]
api_version = "2025-01"

  [[webhooks.subscriptions]]
  uri = "https://api.modularcx.link/morpho/webhooks"
  compliance_topics = [ "customers/data_request", "customers/redact", "shop/redact" ]

  [[webhooks.subscriptions]]
  uri = "https://api.modularcx.link/morpho/webhooks"
  topics = [ "app/uninstalled", "shop/update" ]

[access_scopes]
scopes = "read_products,write_products,read_themes,write_themes"

[auth]
redirect_urls = [ "https://api.modularcx.link/morpho/shopify-app/auth/callback" ]

[pos]
embedded = false
