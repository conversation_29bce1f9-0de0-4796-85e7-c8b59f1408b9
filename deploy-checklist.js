#!/usr/bin/env node

/**
 * Pre-deployment checklist script for Shopify app
 * This script checks if your app meets the requirements for deployment
 */

const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");
const { execSync } = require("child_process");

// Load environment variables
dotenv.config();

// Check environment variables
function checkEnvVariables() {
  const requiredVars = ["SHOPIFY_API_KEY", "SHOPIFY_API_SECRET", "HOST"];
  const missingVars = [];

  requiredVars.forEach((varName) => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    console.error(
      `❌ Missing required environment variables: ${missingVars.join(", ")}`
    );
    return false;
  }

  return true;
}

// Check app configuration
function checkAppConfig() {
  try {
    const appConfig = fs.readFileSync(
      path.join(process.cwd(), "shopify.app.toml"),
      "utf8"
    );

    // Check for mandatory webhooks
    const requiredWebhooks = [
      "app/uninstalled",
      "shop/update",
      "shop/redact",
      "customers/data_request",
      "customers/redact",
    ];

    let missingWebhooks = [];
    requiredWebhooks.forEach((webhook) => {
      if (!appConfig.includes(webhook)) {
        missingWebhooks.push(webhook);
      }
    });

    if (missingWebhooks.length > 0) {
      console.error(
        `❌ Missing required webhooks: ${missingWebhooks.join(", ")}`
      );

      return false;
    }

    // Check for redirect URLs
    if (!appConfig.includes("redirect_urls")) {
      console.error("❌ Missing redirect URLs configuration");
      return false;
    }

    // Check for proper application URL
    if (
      appConfig.includes(
        'application_url = "https://shopify.dev/apps/default-app-home"'
      )
    ) {
      console.error(
        "❌ Using default application URL. Please update to a proper URL."
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error("❌ Error reading app configuration:", error.message);
    return false;
  }
}

// Check server implementation
function checkServerImplementation() {
  try {
    const serverFile = fs.readFileSync(
      path.join(process.cwd(), "server.js"),
      "utf8"
    );

    // Check for webhook verification
    if (
      !serverFile.includes("verifyShopifyWebhook") ||
      !serverFile.includes("hmac")
    ) {
      console.error("❌ Missing webhook verification with HMAC signatures");

      return false;
    }

    // Check for authentication handling
    if (!serverFile.includes("/auth/callback")) {
      console.error("❌ Missing authentication callback handler");
      return false;
    }

    // Check for webhook routes
    const requiredWebhookRoutes = [
      "/webhooks/app/uninstalled",
      "/webhooks/shop/update",
      "/webhooks/shop/redact",
      "/webhooks/customers/data_request",
      "/webhooks/customers/redact",
    ];

    let missingRoutes = [];
    requiredWebhookRoutes.forEach((route) => {
      if (!serverFile.includes(route)) {
        missingRoutes.push(route);
      }
    });

    if (missingRoutes.length > 0) {
      console.error(
        `❌ Missing required webhook routes: ${missingRoutes.join(", ")}`
      );
      return false;
    }

    return true;
  } catch (error) {
    console.error("❌ Error reading server implementation:", error.message);
    return false;
  }
}

// Check for TLS certificate configuration
function checkTLSConfiguration() {
  // Check if the app is configured to use HTTPS
  try {
    const serverFile = fs.readFileSync(
      path.join(process.cwd(), "server.js"),
      "utf8"
    );
    if (!serverFile.includes("https://")) {
      console.warn("⚠️ Warning: Your server code should use HTTPS URLs");
    }
  } catch (error) {
    console.error(
      "❌ Error checking server file for HTTPS usage:",
      error.message
    );
  }

  return true;
}

// Validate the app with Shopify CLI
function validateWithShopifyCLI() {
  try {
    // Run shopify app validate command
    const result = execSync("shopify app validate", { encoding: "utf8" });

    if (result.includes("Validation failed")) {
      console.error("❌ Shopify CLI validation failed");
      return false;
    }

    return true;
  } catch (error) {
    console.error("❌ Error validating with Shopify CLI:", error.message);

    return false;
  }
}

// Run all checks
function runAllChecks() {
  const envCheck = checkEnvVariables();
  const configCheck = checkAppConfig();
  const serverCheck = checkServerImplementation();
  const tlsCheck = checkTLSConfiguration();

  let cliValidation = true;
  try {
    cliValidation = validateWithShopifyCLI();
  } catch (error) {
    console.warn(
      "⚠️ Could not run Shopify CLI validation. Continuing with other checks."
    );
    cliValidation = true; // Don't fail the entire process if CLI validation fails
  }

  if (envCheck && configCheck && serverCheck && tlsCheck && cliValidation) {
    return true;
  } else {
    return false;
  }
}

// Run the checks
runAllChecks();
